"use client"

import { useState } from "react"
import Image from "next/image"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { ImageGalleryModal } from "./image-gallery-modal"
import { Images, Plus } from "lucide-react"
import { cn } from "@/lib/utils"

interface PortfolioImage {
  id: string
  image_url: string
  caption?: string
  display_order: number
}

interface ImageGalleryPreviewProps {
  images: PortfolioImage[]
  businessName: string
  maxPreviewImages?: number
}

export function ImageGalleryPreview({ 
  images, 
  businessName, 
  maxPreviewImages = 6 
}: ImageGalleryPreviewProps) {
  const [isModalOpen, setIsModalOpen] = useState(false)
  const [selectedImageIndex, setSelectedImageIndex] = useState(0)

  // Sort images by display_order
  const sortedImages = [...images].sort((a, b) => a.display_order - b.display_order)

  // Get preview images - if there are more than maxPreviewImages, show one less to make room for the "more" overlay
  const hasMoreImages = sortedImages.length > maxPreviewImages
  const previewCount = hasMoreImages ? maxPreviewImages - 1 : maxPreviewImages
  const previewImages = sortedImages.slice(0, previewCount)
  const remainingCount = Math.max(0, sortedImages.length - previewCount)

  const openModal = (imageIndex: number = 0) => {
    setSelectedImageIndex(imageIndex)
    setIsModalOpen(true)
  }

  const openModalWithAllImages = () => {
    setSelectedImageIndex(0)
    setIsModalOpen(true)
  }

  if (!images.length) {
    return (
      <Card className="bg-neutral-900 border-neutral-800">
        <CardHeader>
          <CardTitle className="text-white flex items-center gap-2">
            <Images className="h-5 w-5" />
            Photo Gallery
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-12">
            <Images className="h-12 w-12 text-neutral-600 mx-auto mb-4" />
            <p className="text-neutral-400 text-lg mb-2">No photos available yet</p>
            <p className="text-neutral-500 text-sm">
              This business hasn't uploaded any portfolio images yet.
            </p>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <>
      <Card className="bg-neutral-900 border-neutral-800">
        <CardHeader className="flex flex-row items-center justify-between">
          <CardTitle className="text-white flex items-center gap-2">
            <Images className="h-5 w-5" />
            Photo Gallery
            <span className="text-neutral-400 text-sm font-normal">
              ({sortedImages.length} photo{sortedImages.length !== 1 ? 's' : ''})
            </span>
          </CardTitle>
          {sortedImages.length > maxPreviewImages && (
            <Button
              variant="outline"
              size="sm"
              onClick={openModalWithAllImages}
              className="border-blue-500/20 text-blue-400 hover:bg-blue-500/10 hover:text-blue-300"
            >
              View All ({sortedImages.length})
            </Button>
          )}
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
            {previewImages.map((image, index) => (
              <div
                key={image.id}
                className={cn(
                  "relative group cursor-pointer rounded-lg overflow-hidden bg-neutral-800",
                  "hover:ring-2 hover:ring-blue-500/50 transition-all duration-200",
                  // Make the first image larger on larger screens
                  index === 0 && previewImages.length > 1 ? "md:col-span-2 md:row-span-2" : ""
                )}
                onClick={() => openModal(index)}
              >
                <div className={cn(
                  "aspect-square relative",
                  index === 0 && previewImages.length > 1 ? "md:aspect-[2/2]" : ""
                )}>
                  <Image
                    src={image.image_url}
                    alt={image.caption || `${businessName} portfolio image ${index + 1}`}
                    fill
                    className="object-cover transition-transform duration-200 group-hover:scale-105"
                    sizes={index === 0 && previewImages.length > 1 
                      ? "(max-width: 768px) 50vw, 33vw" 
                      : "(max-width: 768px) 50vw, 16vw"
                    }
                  />
                  
                  {/* Hover overlay */}
                  <div className="absolute inset-0 bg-black/0 group-hover:bg-black/20 transition-colors duration-200" />
                  
                  {/* Image caption overlay */}
                  {image.caption && (
                    <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/80 to-transparent p-3 opacity-0 group-hover:opacity-100 transition-opacity duration-200">
                      <p className="text-white text-sm font-medium truncate">
                        {image.caption}
                      </p>
                    </div>
                  )}
                </div>
              </div>
            ))}

            {/* Show remaining count overlay if there are more images */}
            {hasMoreImages && (
              <div
                className="relative group cursor-pointer rounded-lg overflow-hidden bg-neutral-800 hover:ring-2 hover:ring-blue-500/50 transition-all duration-200"
                onClick={openModalWithAllImages}
              >
                <div className="aspect-square relative">
                  {/* Background image (last preview image) */}
                  <Image
                    src={previewImages[previewImages.length - 1].image_url}
                    alt="More images"
                    fill
                    className="object-cover"
                    sizes="(max-width: 768px) 50vw, 16vw"
                  />
                  
                  {/* Dark overlay with count */}
                  <div className="absolute inset-0 bg-black/70 flex items-center justify-center">
                    <div className="text-center">
                      <Plus className="h-8 w-8 text-white mx-auto mb-2" />
                      <p className="text-white text-lg font-semibold">
                        +{remainingCount}
                      </p>
                      <p className="text-neutral-300 text-sm">
                        more photo{remainingCount !== 1 ? 's' : ''}
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>

          {/* View All Button for mobile when there are more images */}
          {sortedImages.length > maxPreviewImages && (
            <div className="mt-6 text-center md:hidden">
              <Button
                variant="outline"
                onClick={openModalWithAllImages}
                className="border-blue-500/20 text-blue-400 hover:bg-blue-500/10 hover:text-blue-300"
              >
                <Images className="h-4 w-4 mr-2" />
                View All {sortedImages.length} Photos
              </Button>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Image Gallery Modal */}
      <ImageGalleryModal
        images={sortedImages}
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        initialImageIndex={selectedImageIndex}
        businessName={businessName}
      />
    </>
  )
}
