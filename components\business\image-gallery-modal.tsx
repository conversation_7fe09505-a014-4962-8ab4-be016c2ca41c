"use client"

import { useState, useEffect } from "react"
import Image from "next/image"
import { <PERSON><PERSON>, <PERSON>alog<PERSON>ontent, <PERSON>alogHeader, DialogTitle } from "@/components/ui/dialog"
import { Button } from "@/components/ui/button"
import { ChevronLeft, ChevronRight, X, Download, ZoomIn, ZoomOut } from "lucide-react"
import { cn } from "@/lib/utils"

interface PortfolioImage {
  id: string
  image_url: string
  caption?: string
  display_order: number
}

interface ImageGalleryModalProps {
  images: PortfolioImage[]
  isOpen: boolean
  onClose: () => void
  initialImageIndex?: number
  businessName: string
}

export function ImageGalleryModal({ 
  images, 
  isOpen, 
  onClose, 
  initialImageIndex = 0,
  businessName 
}: ImageGalleryModalProps) {
  const [currentImageIndex, setCurrentImageIndex] = useState(initialImageIndex)
  const [isZoomed, setIsZoomed] = useState(false)

  const currentImage = images[currentImageIndex]

  const goToPrevious = () => {
    setCurrentImageIndex((prev) => (prev === 0 ? images.length - 1 : prev - 1))
    setIsZoomed(false)
  }

  const goToNext = () => {
    setCurrentImageIndex((prev) => (prev === images.length - 1 ? 0 : prev + 1))
    setIsZoomed(false)
  }

  const goToImage = (index: number) => {
    setCurrentImageIndex(index)
    setIsZoomed(false)
  }

  const handleDownload = async () => {
    if (!currentImage) return
    
    try {
      const response = await fetch(currentImage.image_url)
      const blob = await response.blob()
      const url = window.URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.href = url
      link.download = `${businessName}-image-${currentImageIndex + 1}.jpg`
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      window.URL.revokeObjectURL(url)
    } catch (error) {
      console.error('Failed to download image:', error)
    }
  }

  const handleKeyDown = (e: KeyboardEvent) => {
    if (e.key === 'ArrowLeft') goToPrevious()
    if (e.key === 'ArrowRight') goToNext()
    if (e.key === 'Escape') onClose()
  }

  // Add keyboard event listeners
  useEffect(() => {
    if (isOpen) {
      document.addEventListener('keydown', handleKeyDown)
      return () => document.removeEventListener('keydown', handleKeyDown)
    }
  }, [isOpen])

  if (!images.length) return null

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-7xl w-full h-[90vh] bg-black border-neutral-800 p-0 [&>button]:!hidden">
        <DialogHeader className="absolute top-4 left-6 z-10">
          <DialogTitle className="text-white text-lg">
            {businessName} - Photo Gallery ({currentImageIndex + 1} of {images.length})
          </DialogTitle>
        </DialogHeader>

        {/* Custom Close Button */}
        <div className="absolute top-4 right-4 z-50">
          <Button
            variant="ghost"
            size="sm"
            onClick={onClose}
            className="text-white hover:bg-neutral-800 bg-black/50 backdrop-blur-sm"
          >
            <X className="h-5 w-5" />
          </Button>
        </div>

        {/* Main Image Display */}
        <div className="relative flex-1 flex items-center justify-center bg-black">
          {currentImage && (
            <div className={cn(
              "relative max-w-full max-h-full transition-transform duration-200",
              isZoomed ? "scale-150 cursor-zoom-out" : "cursor-zoom-in"
            )}>
              <Image
                src={currentImage.image_url}
                alt={currentImage.caption || `${businessName} portfolio image ${currentImageIndex + 1}`}
                width={1200}
                height={800}
                className="max-w-full max-h-[70vh] object-contain"
                onClick={() => setIsZoomed(!isZoomed)}
                priority
              />
            </div>
          )}

          {/* Navigation Arrows */}
          {images.length > 1 && (
            <>
              <Button
                variant="ghost"
                size="lg"
                onClick={goToPrevious}
                className="absolute left-4 top-1/2 -translate-y-1/2 text-white hover:bg-neutral-800/80 h-12 w-12"
              >
                <ChevronLeft className="h-6 w-6" />
              </Button>
              <Button
                variant="ghost"
                size="lg"
                onClick={goToNext}
                className="absolute right-4 top-1/2 -translate-y-1/2 text-white hover:bg-neutral-800/80 h-12 w-12"
              >
                <ChevronRight className="h-6 w-6" />
              </Button>
            </>
          )}
        </div>

        {/* Bottom Controls */}
        <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/80 to-transparent p-6">
          {/* Image Caption */}
          {currentImage?.caption && (
            <p className="text-white text-center mb-4 text-lg">
              {currentImage.caption}
            </p>
          )}

          {/* Action Buttons */}
          <div className="flex items-center justify-center gap-4 mb-4">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setIsZoomed(!isZoomed)}
              className="text-white hover:bg-neutral-800"
            >
              {isZoomed ? <ZoomOut className="h-4 w-4 mr-2" /> : <ZoomIn className="h-4 w-4 mr-2" />}
              {isZoomed ? 'Zoom Out' : 'Zoom In'}
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={handleDownload}
              className="text-white hover:bg-neutral-800"
            >
              <Download className="h-4 w-4 mr-2" />
              Download
            </Button>
          </div>

          {/* Thumbnail Navigation */}
          {images.length > 1 && (
            <div className="flex items-center justify-center gap-2 overflow-x-auto pb-2">
              {images.map((image, index) => (
                <button
                  key={image.id}
                  onClick={() => goToImage(index)}
                  className={cn(
                    "flex-shrink-0 w-16 h-16 rounded-lg overflow-hidden border-2 transition-all",
                    index === currentImageIndex
                      ? "border-blue-500 ring-2 ring-blue-500/50"
                      : "border-neutral-600 hover:border-neutral-400"
                  )}
                >
                  <Image
                    src={image.image_url}
                    alt={`Thumbnail ${index + 1}`}
                    width={64}
                    height={64}
                    className="w-full h-full object-cover"
                  />
                </button>
              ))}
            </div>
          )}
        </div>
      </DialogContent>
    </Dialog>
  )
}
