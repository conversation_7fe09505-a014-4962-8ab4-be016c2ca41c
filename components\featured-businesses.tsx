"use client"

import { useState, useEffect } from 'react'
import Link from 'next/link'
import { Button } from '@/components/ui/button'
import { useLocation } from '@/hooks/use-location'
import { MapPin, Star, Users, Loader2 } from 'lucide-react'
import type { BusinessWithDetails } from '@/lib/types'

interface FeaturedBusinessesProps {
  fallbackBusinesses?: Array<{
    slug: string
    name: string
    rating: number
    reviews: number
    description: string
  }>
}

export function FeaturedBusinesses({ fallbackBusinesses = [] }: FeaturedBusinessesProps) {
  const { location, loading: locationLoading, error: locationError, requestLocation, hasPermission } = useLocation()
  const [businesses, setBusinesses] = useState<BusinessWithDetails[]>([])
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [showLocationPrompt, setShowLocationPrompt] = useState(true)

  // Fetch businesses based on location
  const fetchNearbyBusinesses = async (lat: number, lng: number) => {
    setLoading(true)
    setError(null)

    try {
      const response = await fetch(`/api/search?latitude=${lat}&longitude=${lng}&limit=9&sortBy=rating`)
      
      if (!response.ok) {
        throw new Error('Failed to fetch businesses')
      }

      const data = await response.json()
      setBusinesses(data.businesses || [])
    } catch (err) {
      console.error('Error fetching nearby businesses:', err)
      setError('Failed to load nearby businesses')
    } finally {
      setLoading(false)
    }
  }

  // Effect to fetch businesses when location is available
  useEffect(() => {
    if (location?.latitude && location?.longitude) {
      fetchNearbyBusinesses(location.latitude, location.longitude)
      setShowLocationPrompt(false)
    }
  }, [location])

  const handleLocationRequest = () => {
    requestLocation()
    setShowLocationPrompt(false)
  }

  const handleSkipLocation = () => {
    setShowLocationPrompt(false)
  }

  // Show location prompt
  if (showLocationPrompt && !hasPermission && !locationError) {
    return (
      <section className="py-16 px-4">
        <div className="container mx-auto">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-white mb-4">Featured Businesses</h2>
            <div className="bg-neutral-900 border border-neutral-800 rounded-xl p-8 max-w-2xl mx-auto">
              <MapPin className="h-12 w-12 text-blue-400 mx-auto mb-4" />
              <h3 className="text-xl font-semibold text-white mb-4">
                Find Businesses Near You
              </h3>
              <p className="text-neutral-400 mb-6">
                Allow location access to see the top-rated pressure washing services in your area.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Button 
                  onClick={handleLocationRequest}
                  className="bg-blue-gradient-hover text-white"
                  disabled={locationLoading}
                >
                  {locationLoading ? (
                    <>
                      <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                      Getting Location...
                    </>
                  ) : (
                    <>
                      <MapPin className="h-4 w-4 mr-2" />
                      Use My Location
                    </>
                  )}
                </Button>
                <Button 
                  variant="outline" 
                  onClick={handleSkipLocation}
                  className="border-neutral-600 text-neutral-300 hover:bg-neutral-800"
                >
                  Skip for Now
                </Button>
              </div>
            </div>
          </div>
        </div>
      </section>
    )
  }

  // Show loading state
  if (loading) {
    return (
      <section className="py-16 px-4">
        <div className="container mx-auto">
          <h2 className="text-3xl font-bold text-white text-center mb-12">
            {location ? `Businesses Near ${location.city || 'You'}` : 'Featured Businesses'}
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {Array.from({ length: 9 }).map((_, i) => (
              <div key={i} className="bg-neutral-900 border border-neutral-800 rounded-xl p-6 animate-pulse">
                <div className="w-full h-48 bg-neutral-800 rounded-xl mb-4"></div>
                <div className="h-6 bg-neutral-800 rounded mb-2"></div>
                <div className="h-4 bg-neutral-800 rounded mb-2 w-3/4"></div>
                <div className="h-4 bg-neutral-800 rounded w-1/2"></div>
              </div>
            ))}
          </div>
        </div>
      </section>
    )
  }

  // Show error state with fallback
  if (error || locationError) {
    return (
      <section className="py-16 px-4">
        <div className="container mx-auto">
          <h2 className="text-3xl font-bold text-white text-center mb-12">Featured Businesses</h2>
          {(error || locationError) && (
            <div className="bg-yellow-500/10 border border-yellow-500/20 rounded-lg p-4 mb-8 max-w-2xl mx-auto">
              <p className="text-yellow-400 text-center">
                {error || locationError} - Showing featured businesses instead.
              </p>
            </div>
          )}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {fallbackBusinesses.slice(0, 9).map((business, i) => (
              <Link
                key={i}
                href={`/business/${business.slug}`}
                className="block transition-transform hover:scale-105"
              >
                <div className="bg-neutral-900 border border-neutral-800 rounded-xl p-6 card-hover-blue">
                  <div className="w-full h-48 bg-neutral-800 rounded-xl mb-4"></div>
                  <h3 className="text-xl font-semibold text-white mb-2">{business.name}</h3>
                  <div className="flex items-center mb-2">
                    <div className="flex text-yellow-400">
                      {"★".repeat(business.rating)}
                      {"☆".repeat(5 - business.rating)}
                    </div>
                    <span className="text-neutral-400 ml-2">({business.reviews} reviews)</span>
                  </div>
                  <p className="text-neutral-400">{business.description}</p>
                </div>
              </Link>
            ))}
          </div>
        </div>
      </section>
    )
  }

  // Show location-based businesses
  return (
    <section className="py-16 px-4">
      <div className="container mx-auto">
        <div className="text-center mb-12">
          <h2 className="text-3xl font-bold text-white mb-2">
            {location?.city ? `Top Businesses in ${location.city}` : 'Featured Businesses Near You'}
          </h2>
          {location?.city && location?.state && (
            <p className="text-neutral-400 flex items-center justify-center gap-2">
              <MapPin className="h-4 w-4" />
              {location.city}, {location.state}
            </p>
          )}
        </div>
        
        {businesses.length > 0 ? (
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {businesses.slice(0, 9).map((business) => (
              <Link
                key={business.id}
                href={`/business/${business.slug}`}
                className="block transition-transform hover:scale-105"
              >
                <div className="bg-neutral-900 border border-neutral-800 rounded-xl p-6 card-hover-blue">
                  {/* Business Image */}
                  <div className="w-full h-48 bg-neutral-800 rounded-xl mb-4 overflow-hidden">
                    {business.portfolio_images?.[0]?.image_url ? (
                      <img
                        src={business.portfolio_images[0].image_url}
                        alt={business.name}
                        className="w-full h-full object-cover"
                      />
                    ) : (
                      <div className="w-full h-full flex items-center justify-center text-neutral-600">
                        <Users className="h-12 w-12" />
                      </div>
                    )}
                  </div>
                  
                  <h3 className="text-xl font-semibold text-white mb-2">{business.name}</h3>
                  
                  <div className="flex items-center mb-2">
                    <div className="flex items-center">
                      <Star className="h-4 w-4 text-yellow-400 fill-current" />
                      <span className="text-white ml-1 font-medium">
                        {business.avg_rating.toFixed(1)}
                      </span>
                    </div>
                    <span className="text-neutral-400 ml-2">
                      ({business.review_count} review{business.review_count !== 1 ? 's' : ''})
                    </span>
                    {business.distance && (
                      <span className="text-blue-400 ml-auto text-sm">
                        {business.distance.toFixed(1)} mi
                      </span>
                    )}
                  </div>
                  
                  <p className="text-neutral-400 line-clamp-2">
                    {business.description || 'Professional pressure washing services'}
                  </p>
                  
                  {business.location && (
                    <p className="text-neutral-500 text-sm mt-2">
                      {business.location.city}, {business.location.state}
                    </p>
                  )}
                </div>
              </Link>
            ))}
          </div>
        ) : (
          <div className="text-center py-12">
            <Users className="h-16 w-16 text-neutral-600 mx-auto mb-4" />
            <h3 className="text-xl font-semibold text-white mb-2">No businesses found nearby</h3>
            <p className="text-neutral-400 mb-6">
              We couldn't find any pressure washing businesses in your area yet.
            </p>
            <Button 
              variant="outline" 
              onClick={handleSkipLocation}
              className="border-neutral-600 text-neutral-300 hover:bg-neutral-800"
            >
              View All Businesses
            </Button>
          </div>
        )}
      </div>
    </section>
  )
}
