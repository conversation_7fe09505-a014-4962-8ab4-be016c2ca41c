import { Header } from "@/components/header"
import { HomepageSearch } from "@/components/homepage-search"
import Link from "next/link"

export default function HomePage() {
  return (
    <div className="min-h-screen bg-black">
      <Header />

      {/* Hero Section */}
      <section className="relative py-20 px-4">
        <div className="container mx-auto text-center">
          <h1 className="text-4xl md:text-6xl font-bold text-white mb-6">
            Find the Best{" "}
            <span className="relative inline-block">
              <span className="absolute inset-0 bg-blue-500/20 rounded-xl blur-sm -m-1"></span>
              <span className="relative text-white">Pressure Washing</span>
            </span>{" "}
            Near You
          </h1>
          <p className="text-xl text-neutral-400 mb-8 max-w-2xl mx-auto">
            Connect with top-rated pressure washing professionals in your area. Compare services, read reviews, and get
            quotes instantly.
          </p>

          {/* Search Section */}
          <div className="max-w-2xl mx-auto">
            <HomepageSearch />
          </div>
        </div>
      </section>

      {/* Featured Businesses */}
      <section className="py-16 px-4">
        <div className="container mx-auto">
          <h2 className="text-3xl font-bold text-white text-center mb-12">Featured Businesses</h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {[
              {
                slug: "elite-pressure-pros",
                name: "Elite Pressure Pros",
                rating: 5,
                reviews: 24,
                description: "Premium residential & commercial pressure washing",
              },
              {
                slug: "clean-slate-services",
                name: "Clean Slate Services",
                rating: 5,
                reviews: 18,
                description: "Eco-friendly cleaning solutions for your property",
              },
              {
                slug: "power-wash-masters",
                name: "Power Wash Masters",
                rating: 4,
                reviews: 31,
                description: "Professional exterior cleaning specialists",
              },
              {
                slug: "crystal-clear-cleaning",
                name: "Crystal Clear Cleaning",
                rating: 5,
                reviews: 15,
                description: "House washing and driveway restoration experts",
              },
              {
                slug: "spotless-solutions",
                name: "Spotless Solutions",
                rating: 4,
                reviews: 22,
                description: "Complete property maintenance and cleaning",
              },
              {
                slug: "pressure-perfect",
                name: "Pressure Perfect",
                rating: 5,
                reviews: 19,
                description: "Reliable service with guaranteed satisfaction",
              },
              {
                slug: "fresh-start-washing",
                name: "Fresh Start Washing",
                rating: 4,
                reviews: 27,
                description: "Transforming properties one wash at a time",
              },
              {
                slug: "pro-clean-exteriors",
                name: "Pro Clean Exteriors",
                rating: 5,
                reviews: 33,
                description: "Commercial and residential pressure washing",
              },
              {
                slug: "shine-bright-services",
                name: "Shine Bright Services",
                rating: 4,
                reviews: 16,
                description: "Making your property look brand new",
              },
            ].map((business, i) => {
              const CardContent = (
                <div className="bg-neutral-900 border border-neutral-800 rounded-xl p-6 card-hover-blue">
                  <div className="w-full h-48 bg-neutral-800 rounded-xl mb-4"></div>
                  <h3 className="text-xl font-semibold text-white mb-2">{business.name}</h3>
                  <div className="flex items-center mb-2">
                    <div className="flex text-yellow-400">
                      {"★".repeat(business.rating)}
                      {"☆".repeat(5 - business.rating)}
                    </div>
                    <span className="text-neutral-400 ml-2">({business.reviews} reviews)</span>
                  </div>
                  <p className="text-neutral-400">{business.description}</p>
                </div>
              )

              if (business.slug) {
                return (
                  <Link
                    key={i}
                    href={`/business/${business.slug}`}
                    className="block transition-transform hover:scale-105"
                  >
                    {CardContent}
                  </Link>
                )
              }

              return <div key={i}>{CardContent}</div>
            })}
          </div>
        </div>
      </section>

      {/* How It Works */}
      <section className="py-16 px-4 bg-neutral-900/50">
        <div className="container mx-auto text-center">
          <h2 className="text-3xl font-bold text-white mb-12">How It Works</h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="text-center">
              <div className="bg-blue-gradient w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4 glow-blue">
                <span className="text-white font-bold text-xl">1</span>
              </div>
              <h3 className="text-xl font-semibold text-white mb-2">Search</h3>
              <p className="text-neutral-400">Find pressure washing services in your area</p>
            </div>
            <div className="text-center">
              <div className="bg-blue-gradient w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4 glow-blue">
                <span className="text-white font-bold text-xl">2</span>
              </div>
              <h3 className="text-xl font-semibold text-white mb-2">Compare</h3>
              <p className="text-neutral-400">Read reviews and compare services</p>
            </div>
            <div className="text-center">
              <div className="bg-blue-gradient w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4 glow-blue">
                <span className="text-white font-bold text-xl">3</span>
              </div>
              <h3 className="text-xl font-semibold text-white mb-2">Hire</h3>
              <p className="text-neutral-400">Get quotes and hire the best professional</p>
            </div>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-neutral-900 border-t border-neutral-800 py-12 px-4">
        <div className="container mx-auto">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            <div>
              <h3 className="text-white font-semibold mb-4">PressureWash Pro</h3>
              <p className="text-neutral-400">The premier directory for pressure washing services.</p>
            </div>
            <div>
              <h4 className="text-white font-medium mb-4">For Customers</h4>
              <ul className="space-y-2 text-neutral-400">
                <li>
                  <a href="/search" className="hover:text-white transition-colors">
                    Find Services
                  </a>
                </li>
                <li>
                  <a href="/how-it-works" className="hover:text-white transition-colors">
                    How It Works
                  </a>
                </li>
              </ul>
            </div>
            <div>
              <h4 className="text-white font-medium mb-4">For Businesses</h4>
              <ul className="space-y-2 text-neutral-400">
                <li>
                  <a href="/auth/signup" className="hover:text-white transition-colors">
                    List Your Business
                  </a>
                </li>
                <li>
                  <a href="/dashboard" className="hover:text-white transition-colors">
                    Business Dashboard
                  </a>
                </li>
              </ul>
            </div>
            <div>
              <h4 className="text-white font-medium mb-4">Support</h4>
              <ul className="space-y-2 text-neutral-400">
                <li>
                  <a href="/contact" className="hover:text-white transition-colors">
                    Contact Us
                  </a>
                </li>
                <li>
                  <a href="/terms" className="hover:text-white transition-colors">
                    Terms of Service
                  </a>
                </li>
              </ul>
            </div>
          </div>
          <div className="border-t border-neutral-800 mt-8 pt-8 text-center text-neutral-400">
            <p>&copy; 2025 PressureWash Pro. All rights reserved.</p>
          </div>
        </div>
      </footer>
    </div>
  )
}
