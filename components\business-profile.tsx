'use client'

import { useState } from 'react'
import { useRouter } from 'next/navigation'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import { Textarea } from '@/components/ui/textarea'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { useUser } from '@/hooks/use-user'
import { useToast } from '@/hooks/use-toast'
import type { BusinessWithDetails, ReviewWithProfile, Review } from '@/lib/types'
import { ReviewForm } from './review-form'
import { ReviewCard } from './review-card'
import {
  Star,
  MapPin,
  Phone,
  Globe,
  MessageSquare,
  StarIcon,
  Calendar,
  User,
  Send,
  Plus
} from 'lucide-react'

interface BusinessProfileProps {
  business: BusinessWithDetails
  initialReviews: ReviewWithProfile[]
}

export function BusinessProfile({ business, initialReviews }: BusinessProfileProps) {
  const { user } = useUser()
  const { toast } = useToast()
  const router = useRouter()
  const [reviews, setReviews] = useState(initialReviews)
  const [showMessageDialog, setShowMessageDialog] = useState(false)
  const [quoteForm, setQuoteForm] = useState({
    // Customer Information
    customerName: '',
    customerEmail: '',
    customerPhone: '',

    // Service Address
    serviceAddress: '',
    serviceCity: '',
    serviceState: '',
    serviceZip: '',

    // Project Details
    serviceType: '',
    propertyType: 'residential', // residential, commercial
    projectDescription: '',
    preferredDate: '',
    urgency: 'flexible', // asap, within_week, within_month, flexible

    // Additional Info
    squareFootage: '',
    additionalNotes: ''
  })
  const [loading, setLoading] = useState(false)

  // Check if user has already reviewed this business
  const userReview = reviews.find(review => review.author_id === user?.id)
  const hasUserReviewed = !!userReview

  const handleReviewSubmitted = (newReview: Review) => {
    setReviews(prev => [newReview as ReviewWithProfile, ...prev])
  }

  const handleReviewUpdated = (updatedReview: Review) => {
    setReviews(prev => prev.map(review =>
      review.id === updatedReview.id ? updatedReview as ReviewWithProfile : review
    ))
  }

  const handleReviewDeleted = (reviewId: string) => {
    setReviews(prev => prev.filter(review => review.id !== reviewId))
  }

  // Form validation
  const isFormValid = () => {
    return (
      quoteForm.customerName.trim() &&
      quoteForm.customerEmail.trim() &&
      quoteForm.customerPhone.trim() &&
      quoteForm.serviceAddress.trim() &&
      quoteForm.serviceCity.trim() &&
      quoteForm.serviceState.trim() &&
      quoteForm.serviceZip.trim() &&
      quoteForm.projectDescription.trim()
    )
  }

  const handleQuoteRequest = async () => {
    if (!isFormValid()) {
      toast({
        title: 'Missing Information',
        description: 'Please fill in all required fields.',
        variant: 'destructive'
      })
      return
    }

    setLoading(true)
    try {
      const response = await fetch(`/api/businesses/${business.slug}/quote-requests`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          businessId: business.id,
          customerInfo: {
            name: quoteForm.customerName,
            email: quoteForm.customerEmail,
            phone: quoteForm.customerPhone
          },
          serviceAddress: {
            street: quoteForm.serviceAddress,
            city: quoteForm.serviceCity,
            state: quoteForm.serviceState,
            zipCode: quoteForm.serviceZip
          },
          projectDetails: {
            propertyType: quoteForm.propertyType,
            serviceType: quoteForm.serviceType,
            squareFootage: quoteForm.squareFootage,
            description: quoteForm.projectDescription,
            additionalNotes: quoteForm.additionalNotes,
            preferredDate: quoteForm.preferredDate,
            urgency: quoteForm.urgency
          }
        })
      })

      if (!response.ok) throw new Error('Failed to send quote request')

      toast({
        title: 'Quote Request Sent!',
        description: `${business.name} will review your request and contact you soon.`
      })

      setShowMessageDialog(false)
      // Reset form
      setQuoteForm({
        customerName: '',
        customerEmail: '',
        customerPhone: '',
        serviceAddress: '',
        serviceCity: '',
        serviceState: '',
        serviceZip: '',
        serviceType: '',
        propertyType: 'residential',
        projectDescription: '',
        preferredDate: '',
        urgency: 'flexible',
        squareFootage: '',
        additionalNotes: ''
      })
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to send quote request. Please try again.',
        variant: 'destructive'
      })
    } finally {
      setLoading(false)
    }
  }

  const renderStars = (rating: number) => {
    return (
      <div className="flex">
        {[1, 2, 3, 4, 5].map((star) => (
          <StarIcon
            key={star}
            className={`h-5 w-5 ${
              star <= rating ? 'text-yellow-400 fill-current' : 'text-neutral-600'
            }`}
          />
        ))}
      </div>
    )
  }

  return (
    <div className="container mx-auto px-4 py-6">
      {/* Business Header */}
      <div className="mb-8">
        <div className="flex flex-col lg:flex-row gap-6">
          {/* Business Info */}
          <div className="flex-1">
            <h1 className="text-4xl font-bold text-white mb-2">{business.name}</h1>
            
            {/* Rating */}
            <div className="flex items-center gap-2 mb-4">
              {renderStars(Math.round(business.avg_rating))}
              <span className="text-white font-medium">{business.avg_rating.toFixed(1)}</span>
              <span className="text-neutral-400">({business.review_count} reviews)</span>
            </div>

            {/* Location */}
            {business.location && (
              <div className="flex items-center gap-2 text-neutral-400 mb-2">
                <MapPin className="h-4 w-4" />
                <span>
                  {business.location.city}, {business.location.state} {business.location.zip_code}
                </span>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Two-Column Layout */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* Main Content - Left Column */}
        <div className="lg:col-span-2 space-y-8">
          {/* About Section */}
          <Card className="bg-neutral-900 border-neutral-800">
            <CardHeader>
              <CardTitle className="text-white">About {business.name}</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-neutral-300 leading-relaxed">
                {business.description || 'No description available.'}
              </p>
            </CardContent>
          </Card>

          {/* Gallery Section */}
          <Card className="bg-neutral-900 border-neutral-800">
            <CardHeader>
              <CardTitle className="text-white">Photo Gallery</CardTitle>
            </CardHeader>
            <CardContent>
              {business.portfolio_images && business.portfolio_images.length > 0 ? (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {business.portfolio_images.map((image) => (
                    <div key={image.id} className="aspect-square bg-neutral-800 rounded-lg overflow-hidden">
                      <img
                        src={image.image_url}
                        alt={image.caption || 'Portfolio image'}
                        className="w-full h-full object-cover hover:scale-105 transition-transform cursor-pointer"
                      />
                    </div>
                  ))}
                </div>
              ) : (
                <p className="text-neutral-400 text-center py-8">No photos available yet.</p>
              )}
            </CardContent>
          </Card>

          {/* Reviews Section */}
          <div className="space-y-6">
            {/* Review Form or Add Review Button */}
            {user && !hasUserReviewed && (
              <ReviewForm
                businessSlug={business.slug}
                businessName={business.name}
                onReviewSubmitted={handleReviewSubmitted}
              />
            )}

            {user && hasUserReviewed && (
              <Card className="bg-neutral-900 border-neutral-800">
                <CardContent className="p-4">
                  <div className="flex items-center justify-between">
                    <span className="text-neutral-400">You have already reviewed this business</span>
                    <Button
                      variant="ghost"
                      size="sm"
                      className="text-blue-400 hover:text-blue-300"
                      onClick={() => {
                        // Navigate to user's review
                        const reviewElement = document.getElementById(`review-${userReview.id}`)
                        reviewElement?.scrollIntoView({ behavior: 'smooth' })
                      }}
                    >
                      View Your Review
                    </Button>
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Reviews List */}
            <Card className="bg-neutral-900 border-neutral-800">
              <CardHeader>
                <CardTitle className="text-white">
                  Customer Reviews ({reviews.length})
                </CardTitle>
              </CardHeader>
              <CardContent>
                {reviews.length > 0 ? (
                  <div className="space-y-6">
                    {reviews.map((review) => (
                      <ReviewCard
                        key={review.id}
                        review={review}
                        businessSlug={business.slug}
                        businessName={business.name}
                        onReviewUpdated={handleReviewUpdated}
                        onReviewDeleted={handleReviewDeleted}
                      />
                    ))}
                  </div>
                ) : (
                  <p className="text-neutral-400 text-center py-8">
                    No reviews yet. Be the first to leave a review!
                  </p>
                )}
              </CardContent>
            </Card>
          </div>
        </div>

        {/* Sidebar - Right Column */}
        <div className="lg:col-span-1">
          <div className="sticky top-8 space-y-6">
            {/* Action Card */}
            <Card className="bg-neutral-900 border-neutral-800">
              <CardContent className="p-6">
                <Button 
                  className="w-full bg-blue-gradient-hover text-white shadow-lg mb-4"
                  onClick={() => setShowMessageDialog(true)}
                >
                  Request Quote
                </Button>
                
                {/* Contact Information */}
                <div className="space-y-3">
                  {business.phone && (
                    <div className="flex items-center gap-3 text-neutral-300">
                      <Phone className="h-4 w-4 text-blue-400" />
                      <span>{business.phone}</span>
                    </div>
                  )}
                  
                  {business.website_url && (
                    <div className="flex items-center gap-3 text-neutral-300">
                      <Globe className="h-4 w-4 text-blue-400" />
                      <a 
                        href={business.website_url} 
                        target="_blank" 
                        rel="noopener noreferrer"
                        className="hover:text-blue-400 transition-colors"
                      >
                        Visit Website
                      </a>
                    </div>
                  )}
                  
                  {business.location && (
                    <div className="flex items-center gap-3 text-neutral-300">
                      <MapPin className="h-4 w-4 text-blue-400" />
                      <span>
                        {business.location.city}, {business.location.state} {business.location.zip_code}
                      </span>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>

            {/* Operating Hours */}
            {business.business_hours && business.business_hours.length > 0 && (
              <Card className="bg-neutral-900 border-neutral-800">
                <CardHeader>
                  <CardTitle className="text-white flex items-center gap-2">
                    <Calendar className="h-4 w-4" />
                    Hours of Operation
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    {business.business_hours.map((hours, index) => {
                      const [day, time] = hours.split(': ')
                      const isToday = new Date().getDay() === ((index + 1) % 7)
                      
                      return (
                        <div 
                          key={index} 
                          className={`flex justify-between items-center py-1 ${
                            isToday ? 'text-blue-400 font-medium' : 'text-neutral-300'
                          }`}
                        >
                          <span className="font-medium">{day}</span>
                          <span className={isToday ? 'text-blue-400' : 'text-neutral-400'}>
                            {time || 'Closed'}
                          </span>
                        </div>
                      )
                    })}
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Services */}
            {business.services && business.services.length > 0 && (
              <Card className="bg-neutral-900 border-neutral-800">
                <CardHeader>
                  <CardTitle className="text-white">Services</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="flex flex-wrap gap-2">
                    {business.services.map((businessService) => (
                      <Badge 
                        key={businessService.service.id} 
                        className="bg-neutral-800 text-neutral-400 border-neutral-700"
                      >
                        {businessService.service.name}
                      </Badge>
                    ))}
                  </div>
                </CardContent>
              </Card>
            )}
          </div>
        </div>
      </div>

      {/* Quote Request Dialog */}
      <Dialog open={showMessageDialog} onOpenChange={setShowMessageDialog}>
        <DialogContent className="bg-neutral-900 border-neutral-800 max-w-2xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle className="text-white">Request a Quote from {business.name}</DialogTitle>
            <p className="text-neutral-400 text-sm">
              Please provide your contact information and project details to receive an accurate quote.
            </p>
          </DialogHeader>

          <div className="space-y-6">
            {/* Customer Information */}
            <div className="space-y-4">
              <h3 className="text-white font-medium">Contact Information</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="customerName" className="text-white">Full Name *</Label>
                  <Input
                    id="customerName"
                    value={quoteForm.customerName}
                    onChange={(e) => setQuoteForm(prev => ({ ...prev, customerName: e.target.value }))}
                    placeholder="Your full name"
                    className="bg-neutral-800 border-neutral-700 text-white placeholder:text-neutral-500"
                  />
                </div>
                <div>
                  <Label htmlFor="customerEmail" className="text-white">Email Address *</Label>
                  <Input
                    id="customerEmail"
                    type="email"
                    value={quoteForm.customerEmail}
                    onChange={(e) => setQuoteForm(prev => ({ ...prev, customerEmail: e.target.value }))}
                    placeholder="<EMAIL>"
                    className="bg-neutral-800 border-neutral-700 text-white placeholder:text-neutral-500"
                  />
                </div>
              </div>
              <div>
                <Label htmlFor="customerPhone" className="text-white">Phone Number *</Label>
                <Input
                  id="customerPhone"
                  type="tel"
                  value={quoteForm.customerPhone}
                  onChange={(e) => setQuoteForm(prev => ({ ...prev, customerPhone: e.target.value }))}
                  placeholder="(*************"
                  className="bg-neutral-800 border-neutral-700 text-white placeholder:text-neutral-500"
                />
              </div>
            </div>

            {/* Service Address */}
            <div className="space-y-4">
              <h3 className="text-white font-medium">Service Address</h3>
              <div>
                <Label htmlFor="serviceAddress" className="text-white">Street Address *</Label>
                <Input
                  id="serviceAddress"
                  value={quoteForm.serviceAddress}
                  onChange={(e) => setQuoteForm(prev => ({ ...prev, serviceAddress: e.target.value }))}
                  placeholder="123 Main Street"
                  className="bg-neutral-800 border-neutral-700 text-white placeholder:text-neutral-500"
                />
              </div>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <div className="col-span-2">
                  <Label htmlFor="serviceCity" className="text-white">City *</Label>
                  <Input
                    id="serviceCity"
                    value={quoteForm.serviceCity}
                    onChange={(e) => setQuoteForm(prev => ({ ...prev, serviceCity: e.target.value }))}
                    placeholder="Charlotte"
                    className="bg-neutral-800 border-neutral-700 text-white placeholder:text-neutral-500"
                  />
                </div>
                <div>
                  <Label htmlFor="serviceState" className="text-white">State *</Label>
                  <Input
                    id="serviceState"
                    value={quoteForm.serviceState}
                    onChange={(e) => setQuoteForm(prev => ({ ...prev, serviceState: e.target.value }))}
                    placeholder="NC"
                    className="bg-neutral-800 border-neutral-700 text-white placeholder:text-neutral-500"
                  />
                </div>
                <div>
                  <Label htmlFor="serviceZip" className="text-white">ZIP Code *</Label>
                  <Input
                    id="serviceZip"
                    value={quoteForm.serviceZip}
                    onChange={(e) => setQuoteForm(prev => ({ ...prev, serviceZip: e.target.value }))}
                    placeholder="28202"
                    className="bg-neutral-800 border-neutral-700 text-white placeholder:text-neutral-500"
                  />
                </div>
              </div>
            </div>

            {/* Project Details */}
            <div className="space-y-4">
              <h3 className="text-white font-medium">Project Details</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="propertyType" className="text-white">Property Type *</Label>
                  <select
                    id="propertyType"
                    value={quoteForm.propertyType}
                    onChange={(e) => setQuoteForm(prev => ({ ...prev, propertyType: e.target.value }))}
                    className="w-full bg-neutral-800 border border-neutral-700 text-white rounded-md px-3 py-2"
                  >
                    <option value="residential">Residential</option>
                    <option value="commercial">Commercial</option>
                  </select>
                </div>
                <div>
                  <Label htmlFor="serviceType" className="text-white">Service Needed</Label>
                  <Input
                    id="serviceType"
                    value={quoteForm.serviceType}
                    onChange={(e) => setQuoteForm(prev => ({ ...prev, serviceType: e.target.value }))}
                    placeholder="e.g., House washing, Driveway cleaning"
                    className="bg-neutral-800 border-neutral-700 text-white placeholder:text-neutral-500"
                  />
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="squareFootage" className="text-white">Approximate Square Footage</Label>
                  <Input
                    id="squareFootage"
                    value={quoteForm.squareFootage}
                    onChange={(e) => setQuoteForm(prev => ({ ...prev, squareFootage: e.target.value }))}
                    placeholder="e.g., 2000 sq ft"
                    className="bg-neutral-800 border-neutral-700 text-white placeholder:text-neutral-500"
                  />
                </div>
                <div>
                  <Label htmlFor="urgency" className="text-white">Timeline</Label>
                  <select
                    id="urgency"
                    value={quoteForm.urgency}
                    onChange={(e) => setQuoteForm(prev => ({ ...prev, urgency: e.target.value }))}
                    className="w-full bg-neutral-800 border border-neutral-700 text-white rounded-md px-3 py-2"
                  >
                    <option value="asap">ASAP</option>
                    <option value="within_week">Within a week</option>
                    <option value="within_month">Within a month</option>
                    <option value="flexible">Flexible</option>
                  </select>
                </div>
              </div>

              <div>
                <Label htmlFor="preferredDate" className="text-white">Preferred Date (Optional)</Label>
                <Input
                  id="preferredDate"
                  type="date"
                  value={quoteForm.preferredDate}
                  onChange={(e) => setQuoteForm(prev => ({ ...prev, preferredDate: e.target.value }))}
                  className="bg-neutral-800 border-neutral-700 text-white"
                />
              </div>

              <div>
                <Label htmlFor="projectDescription" className="text-white">Project Description *</Label>
                <Textarea
                  id="projectDescription"
                  value={quoteForm.projectDescription}
                  onChange={(e) => setQuoteForm(prev => ({ ...prev, projectDescription: e.target.value }))}
                  placeholder="Please describe your pressure washing needs in detail..."
                  className="bg-neutral-800 border-neutral-700 text-white placeholder:text-neutral-500"
                  rows={4}
                />
              </div>

              <div>
                <Label htmlFor="additionalNotes" className="text-white">Additional Notes</Label>
                <Textarea
                  id="additionalNotes"
                  value={quoteForm.additionalNotes}
                  onChange={(e) => setQuoteForm(prev => ({ ...prev, additionalNotes: e.target.value }))}
                  placeholder="Any special requirements, access issues, or other details..."
                  className="bg-neutral-800 border-neutral-700 text-white placeholder:text-neutral-500"
                  rows={3}
                />
              </div>
            </div>

            {/* Submit Button */}
            <div className="pt-4 border-t border-neutral-800">
              <Button
                onClick={handleQuoteRequest}
                disabled={loading || !isFormValid()}
                className="w-full bg-blue-gradient-hover"
              >
                <Send className="h-4 w-4 mr-2" />
                {loading ? 'Sending Quote Request...' : 'Send Quote Request'}
              </Button>
              <p className="text-neutral-500 text-xs mt-2 text-center">
                * Required fields. Your information will only be shared with {business.name}.
              </p>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  )
}
